import { useState } from "react";
import { Keyboard, KeyboardAvoidingView, Platform, StyleSheet, Text, TouchableWithoutFeedback, View } from "react-native";
import { Button, TextInput } from "react-native-paper";
import "./global.css";

export default function App() {
  const [text, setText] = useState("");
  return (
    <KeyboardAvoidingView
    behavior={Platform.OS === "ios" ? "padding" : "height"}
    style={styles.container}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View style={styles.content}>
          <Text style={styles.title}>Welcome!</Text>

            <TextInput
              style={styles.input}
              label="Email"
              keyboardType="email-address"
              placeholder="<EMAIL>"
              mode="outlined"
            />

            <TextInput
              style={styles.input}
              label="Password"
              keyboardType="email-address"
              mode="outlined"
            />

            <Button
            style={styles.button}
            mode="contained"
            >Sign Up</Button>

            <Button
            mode="text"
            >If you have an account, login here...</Button>

        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({

 
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },

  content:{
    flex: 1,
    padding: 20,
    justifyContent: "center",
  },

  title: {
    textAlign: "center",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
  },

  input:{
    marginBottom: 16,
  },

  button:{
    marginTop: 8,
    marginBottom: 16,
  },
  
})
